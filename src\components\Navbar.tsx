
import { <PERSON>, User, <PERSON>, Set<PERSON><PERSON> } from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

interface NavbarProps {
  onShowAlerts: () => void;
  onShowSettings: () => void;
}

export function Navbar({ onShowAlerts, onShowSettings }: NavbarProps) {
  return (
    <header className="border-b border-gray-800 bg-gray-950 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-gray-400 hover:text-blue-400" />
          <div className="flex items-center gap-3">
            <Shield className="h-6 w-6 text-blue-400" />
            <h1 className="text-xl font-bold text-white">AI Security Hub</h1>
          </div>
        </div>
        
        <div className="flex-1 text-center">
          <h2 className="text-lg font-semibold text-gray-200">Dashboard Home</h2>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse-blue"></div>
            <Badge variant="outline" className="border-green-400 text-green-400">
              System Online
            </Badge>
          </div>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={onShowAlerts}
            className="text-gray-400 hover:text-blue-400 relative"
          >
            <Bell className="h-5 w-5" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-blue-400">
                <User className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-gray-900 border-gray-700" align="end">
              <DropdownMenuItem 
                onClick={onShowSettings}
                className="text-gray-200 hover:bg-gray-800 hover:text-blue-400 cursor-pointer"
              >
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem className="text-gray-200 hover:bg-gray-800 hover:text-blue-400">
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
