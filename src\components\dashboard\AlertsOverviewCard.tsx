
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Bug, Package } from "lucide-react";

export function AlertsOverviewCard() {
  const alerts = [
    { type: "Intrusion", count: 3, icon: AlertTriangle, color: "bg-red-600" },
    { type: "Animal", count: 7, icon: Bug, color: "bg-yellow-600" },
    { type: "Misplaced Object", count: 2, icon: Package, color: "bg-orange-600" },
  ];

  return (
    <Card className="bg-gray-900 border-gray-800 hover:border-blue-400 transition-colors">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-blue-400" />
          Alerts Overview
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {alerts.map((alert) => (
          <div key={alert.type} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg hover:bg-gray-750 transition-colors">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${alert.color}`}>
                <alert.icon className="h-4 w-4 text-white" />
              </div>
              <span className="text-gray-200 font-medium">{alert.type}</span>
            </div>
            <Badge variant="outline" className="border-gray-600 text-white">
              {alert.count}
            </Badge>
          </div>
        ))}
        
        <div className="pt-3 border-t border-gray-800">
          <div className="text-center">
            <span className="text-2xl font-bold text-white">12</span>
            <p className="text-gray-400 text-sm">Total alerts this week</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
