
import { SystemHealthCard } from "./dashboard/SystemHealthCard";
import { AlertsOverviewCard } from "./dashboard/AlertsOverviewCard";
import { RecentCapturesPreview } from "./dashboard/ImageTimelineCarousel";
import { SensorEventsTable } from "./dashboard/SensorEventsTable";
import { ObjectSearchForm } from "./dashboard/ObjectSearchForm";

interface DashboardContentProps {
  onSelectItem: (item: any) => void;
}

export function DashboardContent({ onSelectItem }: DashboardContentProps) {
  return (
    <div className="space-y-6 animate-fade-in">
      {/* Top Row - 2 Column Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SystemHealthCard />
        <AlertsOverviewCard />
      </div>

      {/* Middle Section - Recent Captures Preview */}
      <div className="w-full">
        <RecentCapturesPreview onSelectImage={onSelectItem} />
      </div>

      {/* Bottom Row - 2 Column Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SensorEventsTable onSelectEvent={onSelectItem} />
        <ObjectSearchForm />
      </div>
    </div>
  );
}
