
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Camera, Activity, Clock } from "lucide-react";

export function SystemHealthCard() {
  return (
    <Card className="bg-gray-900 border-gray-800 hover:border-blue-400 transition-colors">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Activity className="h-5 w-5 text-blue-400" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-gray-300">
            <Camera className="h-4 w-4" />
            <span>Last Capture</span>
          </div>
          <span className="text-white font-mono">2 min ago</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-gray-300">
            <Clock className="h-4 w-4" />
            <span>Last Sensor Event</span>
          </div>
          <span className="text-white font-mono">5 min ago</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Overall Status</span>
          <Badge className="bg-green-600 hover:bg-green-700">
            All Systems Operational
          </Badge>
        </div>
        
        <div className="pt-2 border-t border-gray-800">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">CPU Usage</span>
            <span className="text-white">23%</span>
          </div>
          <div className="w-full bg-gray-800 rounded-full h-2 mt-1">
            <div className="bg-blue-400 h-2 rounded-full w-1/4"></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
