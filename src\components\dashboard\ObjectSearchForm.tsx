
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Clock } from "lucide-react";

export function ObjectSearchForm() {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);

  const handleSearch = () => {
    // Mock search results
    setSearchResults([
      {
        id: 1,
        timestamp: "Today 12:30 PM",
        thumbnail: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=50&h=50&fit=crop",
        description: "Phone detected on kitchen counter",
        confidence: 92
      },
      {
        id: 2,
        timestamp: "Yesterday 8:45 PM", 
        thumbnail: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=50&h=50&fit=crop",
        description: "Phone seen in living room",
        confidence: 87
      }
    ]);
  };

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Search className="h-5 w-5 text-blue-400" />
          Quick Object Search
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Input
            placeholder="Where did I last keep my phone?"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-400"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <Button
            onClick={handleSearch}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>
        
        {searchResults.length > 0 && (
          <div className="space-y-3 max-h-48 overflow-y-auto">
            {searchResults.map((result) => (
              <div
                key={result.id}
                className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg hover:bg-gray-750 transition-colors cursor-pointer"
              >
                <img
                  src={result.thumbnail}
                  alt="Search result"
                  className="w-10 h-10 rounded object-cover"
                />
                <div className="flex-1">
                  <div className="text-gray-200 text-sm font-medium">
                    {result.description}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    <Clock className="h-3 w-3" />
                    {result.timestamp}
                    <span className="ml-auto text-blue-400">{result.confidence}% confident</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
