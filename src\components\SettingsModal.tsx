
import { X, Save } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <Card className="relative w-full max-w-2xl max-h-[80vh] bg-gray-900 border-gray-800 animate-scale-in">
        <CardHeader className="border-b border-gray-800">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white">System Settings</CardTitle>
            <Button variant="ghost" size="icon" onClick={onClose} className="text-gray-400 hover:text-white">
              <X className="h-5 w-5" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          <Tabs defaultValue="capture" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-gray-800">
              <TabsTrigger value="capture" className="text-gray-300 data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                Capture
              </TabsTrigger>
              <TabsTrigger value="sensors" className="text-gray-300 data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                Sensors
              </TabsTrigger>
              <TabsTrigger value="notifications" className="text-gray-300 data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                Notifications
              </TabsTrigger>
            </TabsList>
            
            <div className="p-6 max-h-96 overflow-y-auto">
              <TabsContent value="capture" className="space-y-6 mt-0">
                <div className="space-y-3">
                  <Label className="text-white text-sm font-medium">Capture Interval (seconds)</Label>
                  <Slider
                    defaultValue={[30]}
                    max={300}
                    min={5}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>5s</span>
                    <span>Current: 30s</span>
                    <span>300s</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <Label className="text-white text-sm font-medium">Image Quality</Label>
                  <Slider
                    defaultValue={[80]}
                    max={100}
                    min={20}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>Low</span>
                    <span>Current: High</span>
                    <span>Ultra</span>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="sensors" className="space-y-6 mt-0">
                <div className="space-y-3">
                  <Label className="text-white text-sm font-medium">Motion Sensitivity</Label>
                  <Slider
                    defaultValue={[70]}
                    max={100}
                    min={10}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>Low</span>
                    <span>Current: 70%</span>
                    <span>High</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <Label className="text-white text-sm font-medium">Audio Detection</Label>
                  <Slider
                    defaultValue={[50]}
                    max={100}
                    min={0}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>Off</span>
                    <span>Current: 50%</span>
                    <span>Max</span>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="notifications" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-white text-sm font-medium">Telegram Notifications</Label>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-white text-sm font-medium">WhatsApp Alerts</Label>
                    <Switch />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-white text-sm font-medium">Email Reports</Label>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-white text-sm font-medium">Push Notifications</Label>
                    <Switch defaultChecked />
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
        
        <div className="border-t border-gray-800 p-4 flex gap-2">
          <Button onClick={onClose} variant="outline" className="flex-1 border-gray-600 text-gray-300 hover:border-blue-400 hover:text-blue-400">
            Cancel
          </Button>
          <Button onClick={onClose} className="flex-1 bg-blue-600 hover:bg-blue-700 text-white">
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </Card>
    </div>
  );
}
