
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON>, Camera, AlertTriangle, Eye } from "lucide-react";

interface SensorEventsTableProps {
  onSelectEvent: (event: any) => void;
}

export function SensorEventsTable({ onSelectEvent }: SensorEventsTableProps) {
  const events = [
    {
      id: 1,
      time: "14:30:45",
      type: "motion",
      icon: AlertTriangle,
      thumbnail: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=50&h=50&fit=crop",
      description: "Motion detected in living room"
    },
    {
      id: 2,
      time: "14:25:12",
      type: "audio",
      icon: Bell,
      thumbnail: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=50&h=50&fit=crop",
      description: "Audio anomaly detected"
    },
    {
      id: 3,
      time: "14:20:33",
      type: "camera",
      icon: Camera,
      thumbnail: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=50&h=50&fit=crop",
      description: "New object detected"
    },
  ];

  return (
    <Card className="bg-card border-border">
      <CardHeader>
        <CardTitle className="text-card-foreground flex items-center gap-2">
          <Bell className="h-5 w-5 text-primary" />
          Recent Sensor Events
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {events.map((event) => (
            <div
              key={event.id}
              className="flex items-center gap-4 p-3 bg-secondary rounded-lg hover:bg-accent transition-colors cursor-pointer group"
              onClick={() => onSelectEvent(event)}
            >
              <div className="text-muted-foreground font-mono text-sm min-w-[70px]">
                {event.time}
              </div>

              <div className="flex items-center gap-2">
                <event.icon className="h-4 w-4 text-primary" />
                <img
                  src={event.thumbnail}
                  alt="Event thumbnail"
                  className="w-8 h-8 rounded object-cover"
                />
              </div>

              <div className="flex-1 text-secondary-foreground text-sm">
                {event.description}
              </div>

              <Button
                variant="ghost"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity text-primary hover:text-primary/80"
              >
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        <div className="mt-4 text-center">
          <Button variant="outline" className="border-border text-muted-foreground hover:border-primary hover:text-primary">
            View All Events
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
