
import { Home, Camera, History, Bell, Settings, Image, AlertTriangle } from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

const menuItems = [
  {
    title: "Home",
    url: "#",
    icon: Home,
  },
  {
    title: "Live View",
    url: "#",
    icon: Camera,
  },
  {
    title: "History Timeline", 
    url: "#",
    icon: History,
  },
  {
    title: "Sensor Events",
    url: "#", 
    icon: Bell,
  },
  {
    title: "Object Search",
    url: "#",
    icon: Image,
  },
  {
    title: "Alerts",
    url: "#",
    icon: AlertTriangle,
  },
  {
    title: "Settings",
    url: "#",
    icon: Settings,
  },
];

export function AppSidebar() {
  return (
    <Sidebar className="border-r border-gray-800">
      <SidebarContent className="bg-gray-950">
        <SidebarGroup>
          <SidebarGroupLabel className="text-blue-400 font-semibold text-sm mb-4">
            AI Security System
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild
                    className="hover:bg-gray-800 hover:text-blue-400 transition-colors group"
                  >
                    <a href={item.url} className="flex items-center gap-3 p-3">
                      <item.icon className="h-5 w-5 group-hover:text-blue-400" />
                      <span className="font-medium">{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
