
import { useState } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { Navbar } from "@/components/Navbar";
import { DashboardContent } from "@/components/DashboardContent";
import { DetailPanel } from "@/components/DetailPanel";
import { AlertsDrawer } from "@/components/AlertsDrawer";
import { SettingsModal } from "@/components/SettingsModal";

const Index = () => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [showAlerts, setShowAlerts] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-black">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <Navbar 
            onShowAlerts={() => setShowAlerts(true)}
            onShowSettings={() => setShowSettings(true)}
          />
          <main className="flex-1 p-6 relative">
            <DashboardContent onSelectItem={setSelectedItem} />
            
            {selectedItem && (
              <DetailPanel 
                item={selectedItem} 
                onClose={() => setSelectedItem(null)} 
              />
            )}
            
            <AlertsDrawer 
              isOpen={showAlerts} 
              onClose={() => setShowAlerts(false)} 
            />
            
            <SettingsModal 
              isOpen={showSettings} 
              onClose={() => setShowSettings(false)} 
            />
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Index;
