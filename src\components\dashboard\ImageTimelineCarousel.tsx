
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Image as ImageIcon } from "lucide-react";

interface ImageTimelineCarouselProps {
  onSelectImage: (image: any) => void;
}

export function ImageTimelineCarousel({ onSelectImage }: ImageTimelineCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const images = [
    { id: 1, timestamp: "14:30", description: "Motion detected in living room", thumbnail: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=200&h=150&fit=crop" },
    { id: 2, timestamp: "14:25", description: "Cat moving through kitchen", thumbnail: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=200&h=150&fit=crop" },
    { id: 3, timestamp: "14:20", description: "Package delivery detected", thumbnail: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=200&h=150&fit=crop" },
    { id: 4, timestamp: "14:15", description: "Normal surveillance", thumbnail: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=200&h=150&fit=crop" },
    { id: 5, timestamp: "14:10", description: "Movement in backyard", thumbnail: "https://images.unsplash.com/photo-1500673922987-e212871fec22?w=200&h=150&fit=crop" },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % images.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <ImageIcon className="h-5 w-5 text-blue-400" />
          Recent Captures
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={prevImage}
              className="text-gray-400 hover:text-blue-400"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            
            <div className="flex-1 flex gap-4 overflow-hidden">
              {images.map((image, index) => (
                <div
                  key={image.id}
                  className={`flex-shrink-0 cursor-pointer transition-all duration-300 ${
                    index === currentIndex ? 'ring-2 ring-blue-400 scale-105' : 'opacity-70 hover:opacity-100'
                  }`}
                  onClick={() => onSelectImage(image)}
                >
                  <div className="relative">
                    <img
                      src={image.thumbnail}
                      alt={image.description}
                      className="w-32 h-24 object-cover rounded-lg"
                    />
                    <div className="absolute bottom-1 left-1 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                      {image.timestamp}
                    </div>
                  </div>
                  <p className="text-xs text-gray-400 mt-1 w-32 truncate">{image.description}</p>
                </div>
              ))}
            </div>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={nextImage}
              className="text-gray-400 hover:text-blue-400"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
