
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Image as ImageIcon } from "lucide-react";

interface RecentCapturesPreviewProps {
  onSelectImage: (image: any) => void;
}

export function RecentCapturesPreview({ onSelectImage }: RecentCapturesPreviewProps) {
  // Show only the 4 most recent captures as preview
  const recentImages = [
    { id: 1, timestamp: "14:30", description: "Motion detected in living room", thumbnail: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=200&h=150&fit=crop" },
    { id: 2, timestamp: "14:25", description: "Cat moving through kitchen", thumbnail: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=200&h=150&fit=crop" },
    { id: 3, timestamp: "14:20", description: "Package delivery detected", thumbnail: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=200&h=150&fit=crop" },
    { id: 4, timestamp: "14:15", description: "Normal surveillance", thumbnail: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=200&h=150&fit=crop" },
  ];

  return (
    <Card className="bg-card border-border">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-card-foreground flex items-center gap-2">
          <ImageIcon className="h-5 w-5 text-primary" />
          Recent Captures
        </CardTitle>
        <Button variant="outline" size="sm" className="border-border text-muted-foreground hover:border-primary hover:text-primary">
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {recentImages.map((image) => (
            <div
              key={image.id}
              className="cursor-pointer transition-all duration-300 hover:scale-105 group"
              onClick={() => onSelectImage(image)}
            >
              <div className="relative">
                <img
                  src={image.thumbnail}
                  alt={image.description}
                  className="w-full h-24 object-cover rounded-lg"
                />
                <div className="absolute bottom-1 left-1 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                  {image.timestamp}
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg" />
              </div>
              <p className="text-xs text-muted-foreground mt-1 truncate">{image.description}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
