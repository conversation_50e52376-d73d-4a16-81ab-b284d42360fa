
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 210 40% 98%;

    --card: 0 0% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 0 0% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 0 0% 20%;
    --primary-foreground: 0 0% 95%;

    --secondary: 0 0% 12%;
    --secondary-foreground: 210 40% 98%;

    --muted: 0 0% 12%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 0 0% 18%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 12%;
    --ring: 0 0% 20%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 4%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 0 0% 20%;
    --sidebar-primary-foreground: 0 0% 95%;
    --sidebar-accent: 0 0% 12%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 0 0% 20%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-black text-foreground;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-pulse-blue {
    animation: pulseBlue 2s infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseBlue {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
